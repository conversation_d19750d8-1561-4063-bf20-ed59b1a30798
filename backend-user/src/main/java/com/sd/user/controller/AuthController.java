package com.sd.user.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.sd.common.api.R;
import com.sd.user.entity.AppUser;
import com.sd.user.service.AppUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户端认证控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AppUserService appUserService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public R<Map<String, Object>> login(@RequestBody Map<String, String> loginForm) {
        try {
            String loginType = loginForm.get("loginType"); // username 或 phone
            String loginValue = loginForm.get("loginValue");
            String password = loginForm.get("password");

            String token = appUserService.login(loginType, loginValue, password);

            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("tokenName", StpUtil.getTokenName());
            result.put("tokenValue", token);
            result.put("isLogin", true);
            result.put("loginId", StpUtil.getLoginId());
            result.put("loginType", StpUtil.getLoginType());
            result.put("tokenTimeout", StpUtil.getTokenTimeout());
            result.put("sessionTimeout", StpUtil.getSessionTimeout());
            result.put("tokenSessionTimeout", StpUtil.getTokenSessionTimeout());
            result.put("tokenActiveTimeout", StpUtil.getTokenActiveTimeout());

            return R.ok("登录成功", result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public R<String> register(@RequestBody AppUser user) {
        try {
            boolean success = appUserService.register(user);
            if (success) {
                return R.ok("注册成功");
            } else {
                return R.fail("注册失败");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public R<String> logout() {
        StpUtil.logout();
        return R.ok("登出成功");
    }

    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/userinfo")
    public R<AppUser> getUserInfo() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            AppUser user = appUserService.getById(userId);
            if (user != null) {
                // 不返回密码
                user.setPassword(null);
                return R.ok("获取用户信息成功", user);
            } else {
                return R.fail("用户不存在");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/isLogin")
    public R<Map<String, Object>> isLogin() {
        Map<String, Object> result = new HashMap<>();
        result.put("isLogin", StpUtil.isLogin());
        result.put("loginId", StpUtil.getLoginId());
        return R.ok("获取登录状态成功", result);
    }

    /**
     * 修改密码
     */
    @PostMapping("/changePassword")
    public R<String> changePassword(@RequestBody Map<String, String> passwordForm) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            String oldPassword = passwordForm.get("oldPassword");
            String newPassword = passwordForm.get("newPassword");

            boolean success = appUserService.changePassword(userId, oldPassword, newPassword);
            if (success) {
                return R.ok("密码修改成功");
            } else {
                return R.fail("密码修改失败");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 设置支付密码（首次设置）
     */
    @PostMapping("/setPayPassword")
    public R<String> setPayPassword(@RequestBody Map<String, String> passwordForm) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            String payPassword = passwordForm.get("payPassword");

            boolean success = appUserService.setPayPassword(userId, payPassword);
            if (success) {
                return R.ok("支付密码设置成功");
            } else {
                return R.fail("支付密码设置失败");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 修改支付密码
     */
    @PostMapping("/changePayPassword")
    public R<String> changePayPassword(@RequestBody Map<String, String> passwordForm) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            String oldPayPassword = passwordForm.get("oldPayPassword");
            String newPayPassword = passwordForm.get("newPayPassword");

            boolean success = appUserService.changePayPassword(userId, oldPayPassword, newPayPassword);
            if (success) {
                return R.ok("支付密码修改成功");
            } else {
                return R.fail("支付密码修改失败");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/updateUserInfo")
    public R<String> updateUserInfo(@RequestBody AppUser user) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            user.setId(userId);

            boolean success = appUserService.updateUserInfo(user);
            if (success) {
                return R.ok("用户信息更新成功");
            } else {
                return R.fail("用户信息更新失败");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
