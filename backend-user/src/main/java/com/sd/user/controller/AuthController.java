package com.sd.user.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.sd.common.api.R;
import com.sd.user.dto.*;
import com.sd.user.entity.AppUser;
import com.sd.user.service.AppUserService;
import com.sd.user.vo.LoginVO;
import com.sd.user.vo.UserInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户端认证控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Tag(name = "用户端认证", description = "用户端认证相关接口")
public class AuthController {

    private final AppUserService appUserService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录")
    public R<LoginVO> login(@Valid @RequestBody LoginDTO loginDTO) {
        try {
            String token = appUserService.login(loginDTO.getLoginType(), loginDTO.getLoginValue(), loginDTO.getPassword());

            // 获取用户信息
            Long userId = StpUtil.getLoginIdAsLong();
            AppUser user = appUserService.getById(userId);

            LoginVO loginVO = new LoginVO();
            loginVO.setToken(token);
            loginVO.setUser(convertToUserInfoVO(user));

            return R.ok("登录成功", loginVO);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册")
    public R<Void> register(@Valid @RequestBody RegisterDTO registerDTO) {
        try {
            AppUser user = new AppUser();
            BeanUtils.copyProperties(registerDTO, user);

            boolean success = appUserService.register(user);
            if (success) {
                return R.ok("注册成功");
            } else {
                return R.fail("注册失败");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出")
    public R<Void> logout() {
        StpUtil.logout();
        return R.ok("登出成功");
    }

    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/userinfo")
    @Operation(summary = "获取当前登录用户信息")
    public R<UserInfoVO> getUserInfo() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            AppUser user = appUserService.getById(userId);
            if (user != null) {
                return R.ok("获取用户信息成功", convertToUserInfoVO(user));
            } else {
                return R.fail("用户不存在");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/isLogin")
    public R<Map<String, Object>> isLogin() {
        Map<String, Object> result = new HashMap<>();
        result.put("isLogin", StpUtil.isLogin());
        result.put("loginId", StpUtil.getLoginId());
        return R.ok("获取登录状态成功", result);
    }

    /**
     * 修改密码
     */
    @PostMapping("/changePassword")
    @Operation(summary = "修改密码")
    public R<Void> changePassword(@Valid @RequestBody ChangePasswordDTO changePasswordDTO) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            boolean success = appUserService.changePassword(userId, changePasswordDTO.getOldPassword(), changePasswordDTO.getNewPassword());
            if (success) {
                return R.ok("密码修改成功");
            } else {
                return R.fail("密码修改失败");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 设置支付密码（首次设置）
     */
    @PostMapping("/setPayPassword")
    @Operation(summary = "设置支付密码")
    public R<Void> setPayPassword(@Valid @RequestBody SetPayPasswordDTO setPayPasswordDTO) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            boolean success = appUserService.setPayPassword(userId, setPayPasswordDTO.getPayPassword());
            if (success) {
                return R.ok("支付密码设置成功");
            } else {
                return R.fail("支付密码设置失败");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 修改支付密码
     */
    @PostMapping("/changePayPassword")
    @Operation(summary = "修改支付密码")
    public R<Void> changePayPassword(@Valid @RequestBody ChangePayPasswordDTO changePayPasswordDTO) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            boolean success = appUserService.changePayPassword(userId, changePayPasswordDTO.getOldPayPassword(), changePayPasswordDTO.getNewPayPassword());
            if (success) {
                return R.ok("支付密码修改成功");
            } else {
                return R.fail("支付密码修改失败");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/updateUserInfo")
    @Operation(summary = "更新用户信息")
    public R<Void> updateUserInfo(@Valid @RequestBody UpdateUserInfoDTO updateUserInfoDTO) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            AppUser user = new AppUser();
            BeanUtils.copyProperties(updateUserInfoDTO, user);
            user.setId(userId);

            boolean success = appUserService.updateUserInfo(user);
            if (success) {
                return R.ok("用户信息更新成功");
            } else {
                return R.fail("用户信息更新失败");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 转换为用户信息VO
     */
    private UserInfoVO convertToUserInfoVO(AppUser user) {
        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(user, userInfoVO);
        return userInfoVO;
    }
}
