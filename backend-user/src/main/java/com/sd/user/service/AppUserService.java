package com.sd.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sd.user.dto.*;
import com.sd.user.entity.AppUser;
import com.sd.user.vo.LoginVO;
import com.sd.user.vo.UserInfoVO;

/**
 * 用户端用户服务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AppUserService extends IService<AppUser> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    AppUser getUserByUsername(String username);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    AppUser getUserByPhone(String phone);

    /**
     * 用户登录
     *
     * @param loginDTO 登录参数
     * @return 登录结果
     */
    LoginVO login(LoginDTO loginDTO);

    /**
     * 用户注册
     *
     * @param registerDTO 注册参数
     */
    void register(RegisterDTO registerDTO);

    /**
     * 用户登出
     */
    void logout();

    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    UserInfoVO getCurrentUserInfo();

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUserInfo(AppUser user);

    /**
     * 修改密码
     *
     * @param changePasswordDTO 修改密码参数
     */
    void changePassword(ChangePasswordDTO changePasswordDTO);

    /**
     * 修改支付密码
     *
     * @param changePayPasswordDTO 修改支付密码参数
     */
    void changePayPassword(ChangePayPasswordDTO changePayPasswordDTO);

    /**
     * 设置支付密码（首次设置）
     *
     * @param setPayPasswordDTO 设置支付密码参数
     */
    void setPayPassword(SetPayPasswordDTO setPayPasswordDTO);

    /**
     * 更新用户信息
     *
     * @param updateUserInfoDTO 更新用户信息参数
     */
    void updateUserInfo(UpdateUserInfoDTO updateUserInfoDTO);
}
