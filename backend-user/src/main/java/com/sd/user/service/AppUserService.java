package com.sd.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sd.user.entity.AppUser;

/**
 * 用户端用户服务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AppUserService extends IService<AppUser> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    AppUser getUserByUsername(String username);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    AppUser getUserByPhone(String phone);

    /**
     * 用户登录
     *
     * @param loginType 登录类型（username/phone）
     * @param loginValue 登录值
     * @param password 密码
     * @return token
     */
    String login(String loginType, String loginValue, String password);

    /**
     * 用户注册
     *
     * @param user 用户信息
     * @return 是否成功
     */
    boolean register(AppUser user);

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUserInfo(AppUser user);

    /**
     * 修改密码
     *
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 修改支付密码
     *
     * @param userId 用户ID
     * @param oldPayPassword 旧支付密码
     * @param newPayPassword 新支付密码
     * @return 是否成功
     */
    boolean changePayPassword(Long userId, String oldPayPassword, String newPayPassword);

    /**
     * 设置支付密码（首次设置）
     *
     * @param userId 用户ID
     * @param payPassword 支付密码
     * @return 是否成功
     */
    boolean setPayPassword(Long userId, String payPassword);

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUserInfo(AppUser user);
}
