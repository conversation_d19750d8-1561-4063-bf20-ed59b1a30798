package com.sd.user.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sd.user.dto.*;
import com.sd.user.entity.AppUser;
import com.sd.user.mapper.AppUserMapper;
import com.sd.user.service.AppUserService;
import com.sd.user.util.InviteCodeUtil;
import com.sd.user.vo.LoginVO;
import com.sd.user.vo.UserInfoVO;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 用户端用户服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class AppUserServiceImpl extends ServiceImpl<AppUserMapper, AppUser> implements AppUserService {

    @Resource
    private PasswordEncoder passwordEncoder;

    @Override
    public AppUser getUserByUsername(String username) {
        if (StrUtil.isBlank(username)) {
            return null;
        }
        LambdaQueryWrapper<AppUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppUser::getUsername, username);
        return this.getOne(queryWrapper);
    }

    @Override
    public AppUser getUserByPhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            return null;
        }
        LambdaQueryWrapper<AppUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppUser::getPhone, phone);
        return this.getOne(queryWrapper);
    }

    @Override
    public LoginVO login(LoginDTO loginDTO) {
        AppUser user = null;

        // 根据登录类型查询用户
        if ("username".equals(loginDTO.getLoginType())) {
            user = getUserByUsername(loginDTO.getLoginValue());
        } else if ("phone".equals(loginDTO.getLoginType())) {
            user = getUserByPhone(loginDTO.getLoginValue());
        }

        Assert.notNull(user, "用户不存在");

        // 验证密码
        Assert.isTrue(passwordEncoder.matches(loginDTO.getPassword(), user.getPassword()), "密码错误");

        // 检查用户状态
        Assert.isTrue(user.getStatus() == 0, "用户已被停用");

        // 更新最后登录信息
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp("");
        this.updateById(user);

        // 登录成功，生成token
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();

        // 构建返回结果
        LoginVO loginVO = new LoginVO();
        loginVO.setToken(token);
        loginVO.setUser(convertToUserInfoVO(user));

        return loginVO;
    }

    @Override
    public void register(RegisterDTO registerDTO) {
        // 检查用户名是否已存在
        Assert.isNull(getUserByUsername(registerDTO.getUsername()), "用户名已存在");

        // 检查手机号是否已存在
        if (StrUtil.isNotBlank(registerDTO.getPhone())) {
            Assert.isNull(getUserByPhone(registerDTO.getPhone()), "手机号已存在");
        }

        // 创建用户实体
        AppUser user = new AppUser();
        BeanUtils.copyProperties(registerDTO, user);

        // 密码加密
        user.setPassword(passwordEncoder.encode(registerDTO.getPassword()));

        // 设置默认状态
        user.setStatus(0);

        // 生成唯一邀请码
        String inviteCode;
        do {
            inviteCode = InviteCodeUtil.generateUniqueInviteCode();
        } while (getByInviteCode(inviteCode) != null);
        user.setInviteCode(inviteCode);

        Assert.isTrue(this.save(user), "注册失败");
    }

    @Override
    public void logout() {
        StpUtil.logout();
    }

    @Override
    public UserInfoVO getCurrentUserInfo() {
        Long userId = StpUtil.getLoginIdAsLong();
        AppUser user = this.getById(userId);
        Assert.notNull(user, "用户不存在");
        return convertToUserInfoVO(user);
    }

    @Override
    public boolean updateUserInfo(AppUser user) {
        // 不允许更新密码、用户名、手机号等敏感信息
        user.setPassword(null);
        user.setUsername(null);
        user.setPhone(null);

        return this.updateById(user);
    }

    @Override
    public void changePassword(ChangePasswordDTO changePasswordDTO) {
        Long userId = StpUtil.getLoginIdAsLong();
        AppUser user = this.getById(userId);
        Assert.notNull(user, "用户不存在");

        // 验证旧密码
        Assert.isTrue(passwordEncoder.matches(changePasswordDTO.getOldPassword(), user.getPassword()), "旧密码错误");

        // 更新新密码
        user.setPassword(passwordEncoder.encode(changePasswordDTO.getNewPassword()));
        Assert.isTrue(this.updateById(user), "密码修改失败");
    }

    @Override
    public void changePayPassword(ChangePayPasswordDTO changePayPasswordDTO) {
        Long userId = StpUtil.getLoginIdAsLong();
        AppUser user = this.getById(userId);
        Assert.notNull(user, "用户不存在");

        // 验证旧支付密码
        Assert.isTrue(StrUtil.isNotBlank(user.getPayPassword()), "尚未设置支付密码");
        Assert.isTrue(passwordEncoder.matches(changePayPasswordDTO.getOldPayPassword(), user.getPayPassword()), "旧支付密码错误");

        // 更新新支付密码
        user.setPayPassword(passwordEncoder.encode(changePayPasswordDTO.getNewPayPassword()));
        Assert.isTrue(this.updateById(user), "支付密码修改失败");
    }

    @Override
    public void setPayPassword(SetPayPasswordDTO setPayPasswordDTO) {
        Long userId = StpUtil.getLoginIdAsLong();
        AppUser user = this.getById(userId);
        Assert.notNull(user, "用户不存在");

        // 检查是否已设置支付密码
        Assert.isTrue(StrUtil.isBlank(user.getPayPassword()), "已设置支付密码，请使用修改功能");

        // 设置支付密码
        user.setPayPassword(passwordEncoder.encode(setPayPasswordDTO.getPayPassword()));
        Assert.isTrue(this.updateById(user), "支付密码设置失败");
    }

    /**
     * 根据邀请码查询用户
     *
     * @param inviteCode 邀请码
     * @return 用户信息
     */
    private AppUser getByInviteCode(String inviteCode) {
        if (StrUtil.isBlank(inviteCode)) {
            return null;
        }
        LambdaQueryWrapper<AppUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppUser::getInviteCode, inviteCode);
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateUserInfo(UpdateUserInfoDTO updateUserInfoDTO) {
        Long userId = StpUtil.getLoginIdAsLong();
        AppUser existUser = this.getById(userId);
        Assert.notNull(existUser, "用户不存在");

        // 只更新允许修改的字段
        if (StrUtil.isNotBlank(updateUserInfoDTO.getNickname())) {
            existUser.setNickname(updateUserInfoDTO.getNickname());
        }
        if (StrUtil.isNotBlank(updateUserInfoDTO.getEmail())) {
            existUser.setEmail(updateUserInfoDTO.getEmail());
        }
        if (StrUtil.isNotBlank(updateUserInfoDTO.getAvatar())) {
            existUser.setAvatar(updateUserInfoDTO.getAvatar());
        }
        if (updateUserInfoDTO.getGender() != null) {
            existUser.setGender(updateUserInfoDTO.getGender());
        }
        if (StrUtil.isNotBlank(updateUserInfoDTO.getBirthday())) {
            existUser.setBirthday(updateUserInfoDTO.getBirthday());
        }
        if (StrUtil.isNotBlank(updateUserInfoDTO.getBio())) {
            existUser.setBio(updateUserInfoDTO.getBio());
        }

        Assert.isTrue(this.updateById(existUser), "用户信息更新失败");
    }

    /**
     * 转换为用户信息VO
     */
    private UserInfoVO convertToUserInfoVO(AppUser user) {
        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(user, userInfoVO);
        return userInfoVO;
    }
}
