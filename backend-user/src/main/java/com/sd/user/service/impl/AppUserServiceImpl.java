package com.sd.user.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sd.user.entity.AppUser;
import com.sd.user.mapper.AppUserMapper;
import com.sd.user.service.AppUserService;
import com.sd.user.util.InviteCodeUtil;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 用户端用户服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class AppUserServiceImpl extends ServiceImpl<AppUserMapper, AppUser> implements AppUserService {

    @Resource
    private PasswordEncoder passwordEncoder;

    @Override
    public AppUser getUserByUsername(String username) {
        if (StrUtil.isBlank(username)) {
            return null;
        }
        LambdaQueryWrapper<AppUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppUser::getUsername, username);
        return this.getOne(queryWrapper);
    }

    @Override
    public AppUser getUserByPhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            return null;
        }
        LambdaQueryWrapper<AppUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppUser::getPhone, phone);
        return this.getOne(queryWrapper);
    }

    @Override
    public String login(String loginType, String loginValue, String password) {
        AppUser user = null;

        // 根据登录类型查询用户
        if ("username".equals(loginType)) {
            user = getUserByUsername(loginValue);
        } else if ("phone".equals(loginType)) {
            user = getUserByPhone(loginValue);
        }

        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证密码
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 检查用户状态
        if (user.getStatus() != 0) {
            throw new RuntimeException("用户已被停用");
        }

        // 更新最后登录信息
        user.setLastLoginTime(LocalDateTime.now());
        // 这里可以获取真实IP，暂时设置为空
        user.setLastLoginIp("");
        this.updateById(user);

        // 登录成功，生成token
        StpUtil.login(user.getId());
        return StpUtil.getTokenValue();
    }

    @Override
    public boolean register(AppUser user) {
        // 检查用户名是否已存在
        if (StrUtil.isNotBlank(user.getUsername()) && getUserByUsername(user.getUsername()) != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查手机号是否已存在
        if (StrUtil.isNotBlank(user.getPhone()) && getUserByPhone(user.getPhone()) != null) {
            throw new RuntimeException("手机号已存在");
        }

        // 密码加密
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // 设置默认状态
        user.setStatus(0);

        // 生成唯一邀请码
        String inviteCode;
        do {
            inviteCode = InviteCodeUtil.generateUniqueInviteCode();
        } while (getByInviteCode(inviteCode) != null); // 确保邀请码唯一
        user.setInviteCode(inviteCode);

        return this.save(user);
    }

    @Override
    public boolean updateUserInfo(AppUser user) {
        // 不允许更新密码、用户名、手机号等敏感信息
        user.setPassword(null);
        user.setUsername(null);
        user.setPhone(null);

        return this.updateById(user);
    }

    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        AppUser user = this.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("旧密码错误");
        }

        // 更新新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        return this.updateById(user);
    }

    @Override
    public boolean changePayPassword(Long userId, String oldPayPassword, String newPayPassword) {
        AppUser user = this.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证旧支付密码
        if (StrUtil.isBlank(user.getPayPassword())) {
            throw new RuntimeException("尚未设置支付密码");
        }
        if (!passwordEncoder.matches(oldPayPassword, user.getPayPassword())) {
            throw new RuntimeException("旧支付密码错误");
        }

        // 更新新支付密码
        user.setPayPassword(passwordEncoder.encode(newPayPassword));
        return this.updateById(user);
    }

    @Override
    public boolean setPayPassword(Long userId, String payPassword) {
        AppUser user = this.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查是否已设置支付密码
        if (StrUtil.isNotBlank(user.getPayPassword())) {
            throw new RuntimeException("已设置支付密码，请使用修改功能");
        }

        // 设置支付密码
        user.setPayPassword(passwordEncoder.encode(payPassword));
        return this.updateById(user);
    }

    /**
     * 根据邀请码查询用户
     *
     * @param inviteCode 邀请码
     * @return 用户信息
     */
    private AppUser getByInviteCode(String inviteCode) {
        if (StrUtil.isBlank(inviteCode)) {
            return null;
        }
        LambdaQueryWrapper<AppUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppUser::getInviteCode, inviteCode);
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean updateUserInfo(AppUser user) {
        if (user == null || user.getId() == null) {
            throw new RuntimeException("用户信息不能为空");
        }

        AppUser existUser = this.getById(user.getId());
        if (existUser == null) {
            throw new RuntimeException("用户不存在");
        }

        // 只更新允许修改的字段
        if (StrUtil.isNotBlank(user.getNickname())) {
            existUser.setNickname(user.getNickname());
        }
        if (StrUtil.isNotBlank(user.getEmail())) {
            existUser.setEmail(user.getEmail());
        }
        if (StrUtil.isNotBlank(user.getAvatar())) {
            existUser.setAvatar(user.getAvatar());
        }
        if (user.getGender() != null) {
            existUser.setGender(user.getGender());
        }
        if (StrUtil.isNotBlank(user.getBirthday())) {
            existUser.setBirthday(user.getBirthday());
        }
        if (StrUtil.isNotBlank(user.getBio())) {
            existUser.setBio(user.getBio());
        }

        return this.updateById(existUser);
    }
}
