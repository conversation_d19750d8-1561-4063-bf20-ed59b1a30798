import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  Button
} from 'antd-mobile';
import {
  UserOutlined,
  PlusOutlined,
  BankOutlined,
  FileTextOutlined,
  HistoryOutlined,
  UserSwitchOutlined,
  MessageOutlined,
  CreditCardOutlined,
  LockOutlined,
  GlobalOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import Layout from '../../components/Layout';
import './index.css';

const Profile: React.FC = () => {

  // 用户信息
  const userInfo = {
    username: 'tom001',
    extensionCode: 'PD2G4M',
    totalBalance: 1351.7280,
    availableBalance: 1351.73,
    frozenAmount: 0.00,
    avatar: null // 未上传头像
  };

  // 功能菜单项
  const menuItems = [
    {
      id: 1,
      title: 'Withdraw',
      icon: 'BankOutlined',
      color: '#10b981',
      path: '/withdraw'
    },
    {
      id: 2,
      title: 'Recharge Details',
      icon: 'FileTextOutlined',
      color: '#3b82f6',
      path: '/recharge-details'
    },
    {
      id: 3,
      title: 'Withdrawal Details',
      icon: 'HistoryOutlined',
      color: '#f59e0b',
      path: '/withdrawal-details'
    },
    {
      id: 4,
      title: 'Account Details',
      icon: 'UserSwitchOutlined',
      color: '#8b5cf6',
      path: '/account-details'
    },
    {
      id: 5,
      title: 'Message',
      icon: 'MessageOutlined',
      color: '#ef4444',
      path: '/message'
    },
    {
      id: 6,
      title: 'Bind Bank Card',
      icon: 'CreditCardOutlined',
      color: '#06b6d4',
      path: '/bind-bank-card'
    },
    {
      id: 7,
      title: 'Change Password',
      icon: 'LockOutlined',
      color: '#84cc16',
      path: '/change-password'
    },
    {
      id: 8,
      title: 'Select Language',
      icon: 'GlobalOutlined',
      color: '#f97316',
      path: '/select-language'
    },
    {
      id: 9,
      title: 'Exit',
      icon: 'LogoutOutlined',
      color: '#dc2626',
      path: '/logout'
    }
  ];



  // 事件处理函数
  const handleMenuClick = (item: any) => {
    console.log('Menu clicked:', item.title);
    if (item.title === 'Exit') {
      // 处理退出登录
      console.log('Logout clicked');
    }
  };

  const handleRechargeClick = () => {
    console.log('Recharge clicked');
  };

  // 格式化货币
  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  // 渲染图标的函数
  const renderIcon = (iconName: string, style?: React.CSSProperties) => {
    const iconProps = {
      style: { fontSize: '20px', ...style }
    };

    switch (iconName) {
      case 'BankOutlined':
        return <BankOutlined {...iconProps} />;
      case 'FileTextOutlined':
        return <FileTextOutlined {...iconProps} />;
      case 'HistoryOutlined':
        return <HistoryOutlined {...iconProps} />;
      case 'UserSwitchOutlined':
        return <UserSwitchOutlined {...iconProps} />;
      case 'MessageOutlined':
        return <MessageOutlined {...iconProps} />;
      case 'CreditCardOutlined':
        return <CreditCardOutlined {...iconProps} />;
      case 'LockOutlined':
        return <LockOutlined {...iconProps} />;
      case 'GlobalOutlined':
        return <GlobalOutlined {...iconProps} />;
      case 'LogoutOutlined':
        return <LogoutOutlined {...iconProps} />;
      default:
        return null;
    }
  };

  return (
    <Layout>
      <div className="profile-page">
        <div className="profile-content">

        {/* 个人信息模块 */}
        <div className="profile-header">
          <Card className="user-card">
            {/* 用户基本信息 */}
            <div className="user-info">
              <div className="user-avatar">
                {userInfo.avatar ? (
                  <img
                    src={userInfo.avatar}
                    alt="Avatar"
                    className="avatar-image"
                  />
                ) : (
                  <UserOutlined className="avatar-icon" />
                )}
              </div>
              <div className="user-details">
                <h2 className="username">{userInfo.username}</h2>
                <div className="user-code">
                  Extension Code: <span className="code-text">{userInfo.extensionCode}</span>
                </div>
              </div>
            </div>

            {/* 余额信息 */}
            <div className="balance-section">
              <div className="balance-header">
                <div className="balance-main">
                  <div className="balance-amount">{formatCurrency(userInfo.totalBalance)}</div>
                  <div className="balance-label">Total Balance</div>
                </div>
                <Button className="add-btn" onClick={handleRechargeClick}>
                  <PlusOutlined />
                </Button>
              </div>

              <div className="balance-details">
                <div className="balance-item">
                  <div className="balance-item-label">Available</div>
                  <div className="balance-item-value available">
                    {formatCurrency(userInfo.availableBalance)}
                  </div>
                </div>
                <div className="balance-item">
                  <div className="balance-item-label">Frozen</div>
                  <div className="balance-item-value frozen">
                    {formatCurrency(userInfo.frozenAmount)}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 功能菜单列表 */}
        <div className="menu-section">
          {/* 账户管理 */}
          <div className="menu-group">
            <h3 className="menu-group-title">Account Management</h3>
            <List className="menu-list">
              {menuItems.slice(0, 4).map((item) => (
                <List.Item
                  key={item.id}
                  prefix={
                    <div className="menu-icon-container">
                      {renderIcon(item.icon, { fontSize: '18px', color: item.color })}
                    </div>
                  }
                  arrow
                  onClick={() => handleMenuClick(item)}
                >
                  {item.title}
                </List.Item>
              ))}
            </List>
          </div>

          {/* 设置选项 */}
          <div className="menu-group">
            <h3 className="menu-group-title">Settings</h3>
            <List className="menu-list">
              {menuItems.slice(4, 8).map((item) => (
                <List.Item
                  key={item.id}
                  prefix={
                    <div className="menu-icon-container">
                      {renderIcon(item.icon, { fontSize: '18px', color: item.color })}
                    </div>
                  }
                  arrow
                  onClick={() => handleMenuClick(item)}
                >
                  {item.title}
                </List.Item>
              ))}
            </List>
          </div>

          {/* 退出登录 */}
          <div className="menu-group">
            <List className="menu-list exit-list">
              <List.Item
                key={menuItems[8].id}
                prefix={
                  <div className="menu-icon-container exit-icon">
                    {renderIcon(menuItems[8].icon, { fontSize: '18px', color: '#ef4444' })}
                  </div>
                }
                arrow
                onClick={() => handleMenuClick(menuItems[8])}
                className="exit-item"
              >
                <span className="exit-title">{menuItems[8].title}</span>
              </List.Item>
            </List>
          </div>
        </div>

        </div>
      </div>
    </Layout>
  );
};

export default Profile;
