-- 添加用户端用户管理权限

-- 1. 添加用户端管理目录
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `permission_key`, `type`, `path`, `component`, `icon`, `sort_order`, `status`, `create_time`, `update_time`, `version`, `create_by`, `update_by`, `deleted`) 
VALUES (200, 0, '用户端管理', 'app', 1, '/app', NULL, 'UserOutlined', 200, 0, NOW(), NOW(), 0, 1, 1, 0);

-- 2. 添加用户端用户管理菜单
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `permission_key`, `type`, `path`, `component`, `icon`, `sort_order`, `status`, `create_time`, `update_time`, `version`, `create_by`, `update_by`, `deleted`) 
VALUES (201, 200, '用户管理', 'app:user', 2, '/app/user', 'System/AppUser', 'TeamOutlined', 1, 0, NOW(), NOW(), 0, 1, 1, 0);

-- 3. 添加用户端用户管理按钮权限
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `permission_key`, `type`, `path`, `component`, `icon`, `sort_order`, `status`, `create_time`, `update_time`, `version`, `create_by`, `update_by`, `deleted`) 
VALUES 
(202, 201, '查看用户', 'app:user:view', 3, NULL, NULL, NULL, 1, 0, NOW(), NOW(), 0, 1, 1, 0),
(203, 201, '编辑用户', 'app:user:edit', 3, NULL, NULL, NULL, 2, 0, NOW(), NOW(), 0, 1, 1, 0),
(204, 201, '重置登录密码', 'app:user:reset:password', 3, NULL, NULL, NULL, 3, 0, NOW(), NOW(), 0, 1, 1, 0),
(205, 201, '重置支付密码', 'app:user:reset:pay:password', 3, NULL, NULL, NULL, 4, 0, NOW(), NOW(), 0, 1, 1, 0);

-- 4. 为超级管理员角色分配这些权限（假设超级管理员角色ID为1）
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `create_time`, `update_time`, `version`, `create_by`, `update_by`, `deleted`) 
VALUES 
(1, 200, NOW(), NOW(), 0, 1, 1, 0),
(1, 201, NOW(), NOW(), 0, 1, 1, 0),
(1, 202, NOW(), NOW(), 0, 1, 1, 0),
(1, 203, NOW(), NOW(), 0, 1, 1, 0),
(1, 204, NOW(), NOW(), 0, 1, 1, 0),
(1, 205, NOW(), NOW(), 0, 1, 1, 0);
